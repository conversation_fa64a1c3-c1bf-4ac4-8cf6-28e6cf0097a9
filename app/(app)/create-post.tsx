import { useState, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  SafeAreaView,
  Platform,
  KeyboardAvoidingView,
  ScrollView,
  Keyboard,
} from "react-native";
import { Stack, router, useLocalSearchParams } from "expo-router";
import { X, ImageIcon } from "lucide-react-native";
import { Image } from "expo-image";
import * as ImagePicker from "expo-image-picker";

import { useFeedPost, useGetUploadUrl } from "@/lib/api/queries";

export default function CreatePost() {
  const { feedGroup, feedId } = useLocalSearchParams<{
    feedGroup: string;
    feedId: string;
  }>();

  const [postText, setPostText] = useState("");
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [selectedImageAsset, setSelectedImageAsset] =
    useState<ImagePicker.ImagePickerAsset | null>(null);
  const [imageAspectRatio, setImageAspectRatio] = useState<number>(1);

  // Ref for text input to control focus
  const textInputRef = useRef<TextInput>(null);

  const postMutation = useFeedPost(feedGroup!, feedId!);
  const getUploadUrl = useGetUploadUrl();

  const showToastMessage = (message: string) => {
    Alert.alert("Success", message);
  };

  // Handle scroll to dismiss keyboard (Twitter-like behavior)
  const handleScroll = () => {
    textInputRef.current?.blur();
    Keyboard.dismiss();
  };

  // Image picker functions
  const openImagePicker = async (source: "camera" | "library") => {
    try {
      let result: ImagePicker.ImagePickerResult;

      if (source === "camera") {
        const { status } = await ImagePicker.requestCameraPermissionsAsync();
        if (status !== "granted") {
          Alert.alert(
            "Permission needed",
            "Camera permission is required to take photos"
          );
          return;
        }

        result = await ImagePicker.launchCameraAsync({
          mediaTypes: "images",
          allowsEditing: true,
          quality: 0.8,
        });
      } else {
        const { status } =
          await ImagePicker.requestMediaLibraryPermissionsAsync();
        if (status !== "granted") {
          Alert.alert(
            "Permission needed",
            "Photo library permission is required to select photos"
          );
          return;
        }

        result = await ImagePicker.launchImageLibraryAsync({
          mediaTypes: "images",
          allowsEditing: true,
          quality: 0.9,
        });
      }

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        setSelectedImage(asset.uri);
        setSelectedImageAsset(asset);

        // Calculate aspect ratio from the cropped image dimensions
        if (asset.width && asset.height) {
          setImageAspectRatio(asset.width / asset.height);
        }
      }
    } catch (error) {
      console.error("Error opening image picker:", error);
      Alert.alert("Error", "Failed to open image picker");
    }
  };

  const handleImagePicker = () => {
    Alert.alert("Select Image", "Choose how you'd like to add an image", [
      { text: "Camera", onPress: () => openImagePicker("camera") },
      { text: "Photo Library", onPress: () => openImagePicker("library") },
      { text: "Cancel", style: "cancel" },
    ]);
  };

  const removeSelectedImage = () => {
    setSelectedImage(null);
    setSelectedImageAsset(null);
    setImageAspectRatio(1);
  };

  const handleCreatePost = async () => {
    if (!postText.trim()) {
      Alert.alert("Error", "Please enter some text for your post");
      return;
    }

    try {
      let attachment = undefined;

      if (selectedImageAsset) {
        // Get content type from the asset
        const getContentType = (uri: string): string => {
          const extension = uri.split(".").pop()?.toLowerCase();
          switch (extension) {
            case "jpg":
            case "jpeg":
              return "image/jpeg";
            case "png":
              return "image/png";
            case "gif":
              return "image/gif";
            case "webp":
              return "image/webp";
            default:
              return "image/jpeg";
          }
        };

        const contentType = getContentType(selectedImageAsset.uri);
        const fileExtension = contentType === "image/png" ? "png" : "jpg";
        const fileName = `post_${Date.now()}.${fileExtension}`;

        // Get upload URL
        const uploadUrlResponse = await new Promise<{
          cdnUrl: string;
          uploadUrl: string;
        }>((resolve, reject) => {
          getUploadUrl.mutate(
            { contentType, fileName },
            {
              onSuccess: resolve,
              onError: reject,
            }
          );
        });

        // Upload the image
        const response = await fetch(selectedImageAsset.uri);
        const blob = await response.blob();

        const uploadResponse = await fetch(uploadUrlResponse.uploadUrl, {
          method: "PUT",
          headers: {
            "Content-Type": contentType,
          },
          body: blob,
        });

        if (!uploadResponse.ok) {
          throw new Error("Failed to upload image");
        }

        attachment = {
          type: "image",
          url: uploadUrlResponse.cdnUrl,
          fileName,
        };
      }

      // Create the post
      await new Promise<void>((resolve, reject) => {
        postMutation.mutate(
          { text: postText, attachment },
          {
            onSuccess: () => resolve(),
            onError: reject,
          }
        );
      });

      showToastMessage("Post created successfully!");
      router.back();
    } catch (error) {
      console.error("Error creating post:", error);
      Alert.alert("Error", "Failed to create post. Please try again.");
    }
  };

  const handleCancel = () => {
    if (postText.trim() || selectedImage) {
      Alert.alert(
        "Discard post?",
        "Are you sure you want to discard this post?",
        [
          { text: "Keep editing", style: "cancel" },
          {
            text: "Discard",
            style: "destructive",
            onPress: () => router.back(),
          },
        ]
      );
    } else {
      router.back();
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen options={{ headerShown: false }} />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={handleCancel} style={styles.headerButton}>
          <X size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Create Post</Text>
        <TouchableOpacity
          style={[
            styles.postButton,
            (!postText.trim() || postMutation.isPending) &&
              styles.postButtonDisabled,
          ]}
          onPress={handleCreatePost}
          disabled={!postText.trim() || postMutation.isPending}
        >
          {postMutation.isPending ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <Text style={styles.postButtonText}>Post</Text>
          )}
        </TouchableOpacity>
      </View>

      {/* Bottom Actions - Fixed at bottom, always visible */}
      <View style={styles.bottomActions}>
        <TouchableOpacity
          style={styles.imageButton}
          onPress={handleImagePicker}
        >
          <ImageIcon size={24} color="#EF5252" />
          <Text style={styles.imageButtonText}>Add Image</Text>
        </TouchableOpacity>
      </View>

      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        keyboardVerticalOffset={0}
      >
        {/* Content with ScrollView for Twitter-like keyboard dismiss */}
        <ScrollView
          style={styles.content}
          contentContainerStyle={styles.contentContainer}
          onScrollBeginDrag={handleScroll}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        >
          <TextInput
            ref={textInputRef}
            style={styles.textInput}
            placeholder="What's on your mind?"
            placeholderTextColor="#9A9A9A"
            value={postText}
            onChangeText={setPostText}
            multiline
            autoFocus
            textAlignVertical="top"
          />

          {selectedImage && (
            <View style={styles.selectedImageContainer}>
              <Image
                source={{ uri: selectedImage }}
                style={styles.selectedImage}
              />
              <TouchableOpacity
                style={styles.removeImageButton}
                onPress={removeSelectedImage}
              >
                <X size={20} color="#fff" />
              </TouchableOpacity>
            </View>
          )}
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#333",
  },
  headerButton: {
    padding: 8,
  },
  headerTitle: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "600",
  },
  postButton: {
    backgroundColor: "#EF5252",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    minWidth: 60,
    alignItems: "center",
  },
  postButtonDisabled: {
    backgroundColor: "#666",
    opacity: 0.5,
  },
  postButtonText: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "600",
  },
  content: {
    flex: 1,
    padding: 16,
    paddingBottom: 0, // Remove bottom padding since bottomActions is now separate
  },
  contentContainer: {
    flexGrow: 1,
    paddingBottom: 20, // Add some bottom padding for scroll content
  },
  textInput: {
    color: "#fff",
    fontSize: 18,
    lineHeight: 24,
    flex: 1,
    textAlignVertical: "top",
  },
  selectedImageContainer: {
    position: "relative",
    marginTop: 16,
  },
  selectedImage: {
    width: "100%",
    aspectRatio: 1, // This will be overridden dynamically
    borderRadius: 8,
    marginTop: 12,
  },
  removeImageButton: {
    position: "absolute",
    top: 8,
    right: 8,
    backgroundColor: "rgba(0, 0, 0, 0.6)",
    borderRadius: 16,
    padding: 4,
  },
  bottomActions: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: "#333",
    backgroundColor: "#000", // Ensure background is visible
  },
  imageButton: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    padding: 8,
  },
  imageButtonText: {
    color: "#EF5252",
    fontSize: 16,
    fontWeight: "500",
  },
});
